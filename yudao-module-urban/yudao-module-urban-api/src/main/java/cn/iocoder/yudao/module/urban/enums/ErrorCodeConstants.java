package cn.iocoder.yudao.module.urban.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {
    // ========== 任务管理 TODO 补充编号 ==========
    ErrorCode TASK_NOT_EXISTS = new ErrorCode(2_000_000_000, "任务管理不存在");
    ErrorCode TASK_TARGET_TYPE_INVALID = new ErrorCode(2_000_000_012, "任务目标类型无效，支持的类型：小区、社区、街区");
    ErrorCode TASK_REGION_ID_REQUIRED = new ErrorCode(2_000_000_013, "区域ID不能为空");
    ErrorCode TASK_REGION_NOT_EXISTS = new ErrorCode(2_000_000_014, "指定的区域不存在");
    // ========== 任务执行人 TODO 补充编号 ==========
    ErrorCode TASK_EXECUTOR_NOT_EXISTS = new ErrorCode(2_000_000_001, "任务执行人不存在");

    // ========== 行政区划 TODO 补充编号 ==========
    ErrorCode REGION_NOT_EXISTS = new ErrorCode(2_000_000_002, "行政区划不存在");
    ErrorCode REGION_EXITS_CHILDREN = new ErrorCode(2_000_000_003, "存在存在子行政区划，无法删除");
    ErrorCode REGION_PARENT_NOT_EXITS = new ErrorCode(2_000_000_004,"父级行政区划不存在");
    ErrorCode REGION_PARENT_ERROR = new ErrorCode(2_000_000_005, "不能设置自己为父行政区划");
    ErrorCode REGION_REGION_NAME_DUPLICATE = new ErrorCode(2_000_000_006, "已经存在该地区名称的行政区划");
    ErrorCode REGION_PARENT_IS_CHILD = new ErrorCode(2_000_000_007, "不能设置自己的子Region为父Region");

    // ========== 城市标准体体系 TODO 补充编号 ==========
    ErrorCode CITYSTANDARD_NOT_EXISTS = new ErrorCode(2_000_000_008, "城市标准体体系不存在");
    // ========== 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 TODO 补充编号 ==========
    ErrorCode CITYSTANDARD_ITEM_NOT_EXISTS = new ErrorCode(2_000_000_009, "指标目标不存在");
    // ========== 指标目标采集表单 TODO 补充编号 ==========
    ErrorCode CITYSTANDARD_ITEM_FORM_NOT_EXISTS = new ErrorCode(2_000_000_010, "指标目标采集表单不存在");

    // ========== 空间数据-住房 TODO 补充编号 ==========
    ErrorCode BASIC_HOUSE_NOT_EXISTS = new ErrorCode(2_000_000_011, "空间数据-住房不存在");
    // ========== 任务关联业务 TODO 补充编号 ==========
    ErrorCode RS_TASK_BUSINESS_NOT_EXISTS = new ErrorCode(2_000_000_012, "任务关联业务不存在");
}

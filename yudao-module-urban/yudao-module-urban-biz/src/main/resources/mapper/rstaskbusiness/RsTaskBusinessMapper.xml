<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.urban.dal.mysql.rstaskbusiness.RsTaskBusinessMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 根据任务ID获取关联的住房数据 -->
    <select id="selectHousesByTaskId" resultType="cn.iocoder.yudao.module.urban.controller.admin.rstaskbusiness.vo.TaskHouseRespVO">
        SELECT
            h.id,
            ST_AsText(h.geom) AS geom,
            h.height,
            h.status,
            h.create_time,
            h.update_time
        FROM uc_rs_task_business rtb
        INNER JOIN uc_basic_house h ON rtb.business_id = h.id::text
        WHERE rtb.task_id = #{taskId}
          AND rtb.task_type = 'ZF'
          AND rtb.deleted = 0
          AND h.deleted = 0
        ORDER BY h.id
    </select>

</mapper>
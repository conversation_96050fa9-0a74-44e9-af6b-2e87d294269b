package cn.iocoder.yudao.module.urban.controller.admin.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Schema(description = "管理后台 - 任务管理批量创建 Request VO")
@Data
public class TaskBatchCreateReqVO {

    @Schema(description = "城市标准体系ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "城市标准体系ID不能为空")
    private Long citystandardId;

    @Schema(description = "目标类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "社区")
    @NotEmpty(message = "目标类型不能为空")
    private String targetType;

    @Schema(description = "任务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "任务类型不能为空")
    private String taskType;

    @Schema(description = "是否手动分割",  example = "true")
    private Boolean manualSplit;

    @Schema(description = "分割数量",  example = "4")
    @Min(value = 1, message = "分割数量必须大于0")
    private Integer splitCount;

    @Schema(description = "区域ID（社区ID）", example = "c3f8e0f2-4d36-4407-bace-a8b317a1dd47")
    private String regionId;

}

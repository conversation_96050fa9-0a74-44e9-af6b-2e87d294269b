package cn.iocoder.yudao.module.urban.service.task;

import cn.iocoder.yudao.module.urban.controller.admin.taskexecutor.vo.TaskExecutorSaveReqVO;
import cn.iocoder.yudao.module.urban.dal.dataobject.region.RegionDO;
import cn.iocoder.yudao.module.urban.dal.dataobject.taskexecutor.TaskExecutorDO;
import cn.iocoder.yudao.module.urban.dal.dataobject.task.CommunitySubregionDO;
import cn.iocoder.yudao.module.urban.dal.mysql.taskexecutor.TaskExecutorMapper;
import cn.iocoder.yudao.module.urban.service.region.RegionService;
import cn.iocoder.yudao.module.urban.service.taskexecutor.TaskExecutorService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.urban.controller.admin.task.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.task.TaskDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.urban.dal.mysql.task.TaskMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.urban.enums.ErrorCodeConstants.*;

/**
 * 任务管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TaskServiceImpl implements TaskService {

    @Resource
    private TaskMapper taskMapper;
    @Resource
    private TaskExecutorService taskExecutorService;
    @Autowired
    private RegionService regionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTask(TaskSaveReqVO createReqVO) {
        // 插入
        TaskDO task = BeanUtils.toBean(createReqVO, TaskDO.class);
        taskMapper.insert(task);

        List<Long> surveyorIds = createReqVO.getSurveyorIds();
        if(!surveyorIds.isEmpty()){
            // 添加新数据
            surveyorIds.forEach(surveyorId -> {
                TaskExecutorSaveReqVO taskExecutorSaveReqVO = new TaskExecutorSaveReqVO();
                taskExecutorSaveReqVO.setTaskId(task.getId());
                taskExecutorSaveReqVO.setUserId(surveyorId);
                taskExecutorSaveReqVO.setUserType("DCY");
                taskExecutorService.createTaskExecutor(taskExecutorSaveReqVO);
            });
        }

        //todo 需要对不同类型的  业务数据进行绑定taskid
        // 返回
        return task.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchCreateTasks(TaskBatchCreateReqVO batchCreateReqVO) {
        List<Long> taskIds = new ArrayList<>();



        // 验证targetType是否为支持的类型
        if (!isValidTargetType(batchCreateReqVO.getTargetType())) {
            throw exception(TASK_TARGET_TYPE_INVALID);
        }

        // 根据targetType和分割策略创建任务
        if ("社区".equals(batchCreateReqVO.getTargetType())) {
            // 社区手动分割逻辑
            taskIds = createCommunityTasks(batchCreateReqVO);
        } else {
            // 其他类型或非手动分割的默认逻辑
            taskIds = createDefaultTasks(batchCreateReqVO);
        }

        return taskIds;
    }

    /**
     * 验证目标类型是否有效
     */
    private boolean isValidTargetType(String targetType) {
        return Arrays.asList("小区", "社区", "街区").contains(targetType);
    }

    /**
     * 创建社区任务（基于房屋聚类分割）
     */
    private List<Long> createCommunityTasks(TaskBatchCreateReqVO batchCreateReqVO) {
        List<Long> taskIds = new ArrayList<>();

        // 暂时使用固定的社区ID，实际应该根据region获取

        RegionDO regionDO = regionService.getRegion("c3f8e0f2-4d36-4407-bace-a8b317a1dd47");

        // 调用分割查询
        List<CommunitySubregionDO> subregions = taskMapper.selectCommunitySubregionsBySplit(regionDO.getGeom(),batchCreateReqVO.getSplitCount());

        // 为每个子区域创建任务
        for (int i = 0; i < subregions.size(); i++) {
            CommunitySubregionDO subregion = subregions.get(i);
            TaskSaveReqVO taskSaveReqVO = new TaskSaveReqVO();

            // 设置基本信息
            taskSaveReqVO.setCitystandardId(batchCreateReqVO.getCitystandardId());
            taskSaveReqVO.setTaskType(batchCreateReqVO.getTaskType());

            // 生成任务编号和名称
            String taskId = generateTaskId(batchCreateReqVO.getTaskType(), i + 1);
            String taskName = generateTaskName(regionDO.getName(),
                                             subregions.size(), i + 1);

            taskSaveReqVO.setTaskId(taskId);
            taskSaveReqVO.setTaskName(taskName);

            // 设置状态
            taskSaveReqVO.setStatus( 0); // 未完成

            // 设置子区域的几何图形
            taskSaveReqVO.setGeom(subregion.getGeom());

            // 创建任务
            Long createdTaskId = this.createTask(taskSaveReqVO);
            taskIds.add(createdTaskId);
        }

        return taskIds;
    }

    /**
     * 创建默认任务（非社区手动分割）
     */
    private List<Long> createDefaultTasks(TaskBatchCreateReqVO batchCreateReqVO) {
        List<Long> taskIds = new ArrayList<>();

        // 根据分割策略创建任务
        for (int i = 1; i <= batchCreateReqVO.getSplitCount(); i++) {
            TaskSaveReqVO taskSaveReqVO = new TaskSaveReqVO();

            // 设置基本信息
            taskSaveReqVO.setCitystandardId(batchCreateReqVO.getCitystandardId());
            taskSaveReqVO.setTaskType(batchCreateReqVO.getTargetType());

            // 生成任务编号和名称
            String taskId = generateTaskId(batchCreateReqVO.getTaskType(), i);
            String taskName = generateTaskName(batchCreateReqVO.getTargetType(),
                                             batchCreateReqVO.getSplitCount(), i);

            taskSaveReqVO.setTaskId(taskId);
            taskSaveReqVO.setTaskName(taskName);

            // 设置默认状态
            taskSaveReqVO.setStatus(0); // 未完成

            // 设置默认图形（可根据实际需求调整）
//            taskSaveReqVO.setGeom("POINT(0 0)");

            // 创建任务
            Long createdTaskId = createTask(taskSaveReqVO);
            taskIds.add(createdTaskId);
        }

        return taskIds;
    }

    /**
     * 生成任务编号
     */
    private String generateTaskId(String taskType, int index) {
        return String.format("%s%d%03d", taskType,System.currentTimeMillis(),index);
    }

    /**
     * 生成任务名称
     */
    private String generateTaskName(String targetType, int totalCount, int index) {
        return String.format("%s调查任务 (%d/%d)", targetType, index, totalCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTask(TaskSaveReqVO updateReqVO) {
        // 校验存在
        validateTaskExists(updateReqVO.getId());
        // 更新
        TaskDO updateObj = BeanUtils.toBean(updateReqVO, TaskDO.class);
        taskMapper.updateById(updateObj);

        List<Long> surveyorIds = updateReqVO.getSurveyorIds();
        if(!surveyorIds.isEmpty()){
            // 删除原有数据
            taskExecutorService.deleteTaskExecutorbyTaskId(updateReqVO.getId());
            // 添加新数据
            surveyorIds.forEach(surveyorId -> {
                TaskExecutorSaveReqVO taskExecutorSaveReqVO = new TaskExecutorSaveReqVO();
                taskExecutorSaveReqVO.setTaskId(updateReqVO.getId());
                taskExecutorSaveReqVO.setUserId(surveyorId);
                taskExecutorSaveReqVO.setUserType("DCY");
                taskExecutorService.createTaskExecutor(taskExecutorSaveReqVO);
            });
        }

        //todo 需要更新对不同类型的  业务数据进行绑定taskid
    }

    @Override
    public void deleteTask(Long id) {
        // 校验存在
        validateTaskExists(id);
        // 删除
        taskMapper.deleteById(id);
        //删除相关 调查人员记录
        taskExecutorService.deleteTaskExecutorbyTaskId(id);
        //todo 需要删除对应业务数据的 taskid
    }

    private void validateTaskExists(Long id) {
        if (taskMapper.selectById(id) == null) {
            throw exception(TASK_NOT_EXISTS);
        }
    }

    @Override
    public TaskDO getTask(Long id) {

        TaskDO task = taskMapper.selectById(id);
        if (task == null) {
            return null;
        }
        // 2. 使用关联查询获取执行人ID列表
        List<Long> surveyorIds = taskMapper.selectJoinList(
                Long.class,
                new MPJLambdaWrapper<TaskDO>()
                        .select(TaskExecutorDO::getUserId)
                        .leftJoin(TaskExecutorDO.class, TaskExecutorDO::getTaskId, TaskDO::getId)
                        .eq(TaskDO::getId, id)
                        .isNotNull(TaskExecutorDO::getUserId)
        );

        // 3. 设置执行人ID列表
        task.setSurveyorIds(surveyorIds != null ? surveyorIds : new ArrayList<>());

        return task;

    }

    @Override
    public PageResult<TaskDO> getTaskPage(TaskPageReqVO pageReqVO) {
        // 构建分页参数
        Page<TaskDO> page = MyBatisUtils.buildPage(pageReqVO);

        // 使用新的关联查询方法
        Page<TaskDO> resultPage = taskMapper.selectPageWithExecutors(page, pageReqVO);

        // 解析执行人ID字符串并设置到任务对象中
        resultPage.getRecords().forEach(task -> {
            List<Long> surveyorIds = parseSurveyorIds(task.getSurveyorIdsStr());
            task.setSurveyorIds(surveyorIds);
        });

        // 转换为 PageResult
        return new PageResult<>(resultPage.getRecords(), resultPage.getTotal());
    }

    /**
     * 解析执行人ID字符串为ID列表
     *
     * @param surveyorIdsStr 执行人ID字符串，格式如 "1,2,3"
     * @return 执行人ID列表
     */
    private List<Long> parseSurveyorIds(String surveyorIdsStr) {
        if (surveyorIdsStr == null || surveyorIdsStr.trim().isEmpty()) {
            return new ArrayList<>();
        }

        return Arrays.stream(surveyorIdsStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

}
package cn.iocoder.yudao.module.urban.controller.admin.rstaskbusiness.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 任务关联业务新增/修改 Request VO")
@Data
public class RsTaskBusinessSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1873")
    private Long id;

    @Schema(description = "城市标准体系ID", example = "8454")
    private Long citystandardId;

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24153")
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    @Schema(description = "任务类型：住宅|小区|社区|街道", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "任务类型：住宅|小区|社区|街道不能为空")
    private String taskType;

    @Schema(description = "业务ID ，采集对象如住宅、小区、社区", requiredMode = Schema.RequiredMode.REQUIRED, example = "29724")
    @NotEmpty(message = "业务ID ，采集对象如住宅、小区、社区不能为空")
    private String businessId;

}
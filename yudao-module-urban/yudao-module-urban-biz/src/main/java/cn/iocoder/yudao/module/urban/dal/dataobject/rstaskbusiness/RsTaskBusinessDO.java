package cn.iocoder.yudao.module.urban.dal.dataobject.rstaskbusiness;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 任务关联业务 DO
 *
 * <AUTHOR>
 */
@TableName("uc_rs_task_business")
@KeySequence("uc_rs_task_business_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RsTaskBusinessDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 城市标准体系ID
     */
    private Long citystandardId;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 任务类型：住宅|小区|社区|街道
     */
    private String taskType;
    /**
     * 业务ID ，采集对象如住宅、小区、社区
     */
    private String businessId;

}
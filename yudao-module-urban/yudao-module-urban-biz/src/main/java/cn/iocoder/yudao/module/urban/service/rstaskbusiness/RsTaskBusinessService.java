package cn.iocoder.yudao.module.urban.service.rstaskbusiness;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.urban.controller.admin.rstaskbusiness.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.rstaskbusiness.RsTaskBusinessDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 任务关联业务 Service 接口
 *
 * <AUTHOR>
 */
public interface RsTaskBusinessService {

    /**
     * 创建任务关联业务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRsTaskBusiness(@Valid RsTaskBusinessSaveReqVO createReqVO);

    /**
     * 批量创建任务关联业务
     *
     * @param createReqVOList 批量创建信息列表
     * @return 是否创建成功
     */
    Boolean batchCreateRsTaskBusiness(@Valid List<RsTaskBusinessSaveReqVO> createReqVOList);

    /**
     * 更新任务关联业务
     *
     * @param updateReqVO 更新信息
     */
    void updateRsTaskBusiness(@Valid RsTaskBusinessSaveReqVO updateReqVO);

    /**
     * 删除任务关联业务
     *
     * @param id 编号
     */
    void deleteRsTaskBusiness(Long id);

    /**
     * 获得任务关联业务
     *
     * @param id 编号
     * @return 任务关联业务
     */
    RsTaskBusinessDO getRsTaskBusiness(Long id);

    /**
     * 获得任务关联业务分页
     *
     * @param pageReqVO 分页查询
     * @return 任务关联业务分页
     */
    PageResult<RsTaskBusinessDO> getRsTaskBusinessPage(RsTaskBusinessPageReqVO pageReqVO);

    /**
     * 根据任务ID获取关联的住房数据
     *
     * @param taskId 任务ID
     * @return 住房数据列表
     */
    List<TaskHouseRespVO> getHousesByTaskId(Long taskId);

}
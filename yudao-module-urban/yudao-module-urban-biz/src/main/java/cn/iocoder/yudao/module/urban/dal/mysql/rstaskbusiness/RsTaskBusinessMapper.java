package cn.iocoder.yudao.module.urban.dal.mysql.rstaskbusiness;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.urban.dal.dataobject.rstaskbusiness.RsTaskBusinessDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import cn.iocoder.yudao.module.urban.controller.admin.rstaskbusiness.vo.*;

/**
 * 任务关联业务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RsTaskBusinessMapper extends BaseMapperX<RsTaskBusinessDO> {

    default PageResult<RsTaskBusinessDO> selectPage(RsTaskBusinessPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RsTaskBusinessDO>()
                .eqIfPresent(RsTaskBusinessDO::getCitystandardId, reqVO.getCitystandardId())
                .eqIfPresent(RsTaskBusinessDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(RsTaskBusinessDO::getTaskType, reqVO.getTaskType())
                .eqIfPresent(RsTaskBusinessDO::getBusinessId, reqVO.getBusinessId())
                .betweenIfPresent(RsTaskBusinessDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RsTaskBusinessDO::getId));
    }

    /**
     * 根据任务ID获取关联的住房数据
     *
     * @param taskId 任务ID
     * @return 住房数据列表
     */
    List<TaskHouseRespVO> selectHousesByTaskId(@Param("taskId") Long taskId);

}
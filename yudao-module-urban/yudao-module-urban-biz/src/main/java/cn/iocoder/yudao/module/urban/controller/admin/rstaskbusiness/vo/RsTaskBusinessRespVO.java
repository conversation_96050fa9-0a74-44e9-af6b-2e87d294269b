package cn.iocoder.yudao.module.urban.controller.admin.rstaskbusiness.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 任务关联业务 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RsTaskBusinessRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1873")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "城市标准体系ID", example = "8454")
    @ExcelProperty("城市标准体系ID")
    private Long citystandardId;

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24153")
    @ExcelProperty("任务ID")
    private Long taskId;

    @Schema(description = "任务类型：住宅|小区|社区|街道", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("任务类型：住宅|小区|社区|街道")
    private String taskType;

    @Schema(description = "业务ID ，采集对象如住宅、小区、社区", requiredMode = Schema.RequiredMode.REQUIRED, example = "29724")
    @ExcelProperty("业务ID ，采集对象如住宅、小区、社区")
    private String businessId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
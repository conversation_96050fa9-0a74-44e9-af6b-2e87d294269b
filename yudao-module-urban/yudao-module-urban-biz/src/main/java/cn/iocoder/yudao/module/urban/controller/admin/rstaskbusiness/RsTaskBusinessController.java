package cn.iocoder.yudao.module.urban.controller.admin.rstaskbusiness;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.urban.controller.admin.rstaskbusiness.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.rstaskbusiness.RsTaskBusinessDO;
import cn.iocoder.yudao.module.urban.service.rstaskbusiness.RsTaskBusinessService;

@Tag(name = "管理后台 - 任务关联业务")
@RestController
@RequestMapping("/urban/rs-task-business")
@Validated
public class RsTaskBusinessController {

    @Resource
    private RsTaskBusinessService rsTaskBusinessService;

    @PostMapping("/create")
    @Operation(summary = "创建任务关联业务")
    public CommonResult<Long> createRsTaskBusiness(@Valid @RequestBody RsTaskBusinessSaveReqVO createReqVO) {
        return success(rsTaskBusinessService.createRsTaskBusiness(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新任务关联业务")
    public CommonResult<Boolean> updateRsTaskBusiness(@Valid @RequestBody RsTaskBusinessSaveReqVO updateReqVO) {
        rsTaskBusinessService.updateRsTaskBusiness(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除任务关联业务")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteRsTaskBusiness(@RequestParam("id") Long id) {
        rsTaskBusinessService.deleteRsTaskBusiness(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得任务关联业务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<RsTaskBusinessRespVO> getRsTaskBusiness(@RequestParam("id") Long id) {
        RsTaskBusinessDO rsTaskBusiness = rsTaskBusinessService.getRsTaskBusiness(id);
        return success(BeanUtils.toBean(rsTaskBusiness, RsTaskBusinessRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得任务关联业务分页")
    @PreAuthorize("@ss.hasPermission('urban:rs-task-business:query')")
    public CommonResult<PageResult<RsTaskBusinessRespVO>> getRsTaskBusinessPage(@Valid RsTaskBusinessPageReqVO pageReqVO) {
        PageResult<RsTaskBusinessDO> pageResult = rsTaskBusinessService.getRsTaskBusinessPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RsTaskBusinessRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出任务关联业务 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRsTaskBusinessExcel(@Valid RsTaskBusinessPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RsTaskBusinessDO> list = rsTaskBusinessService.getRsTaskBusinessPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "任务关联业务.xls", "数据", RsTaskBusinessRespVO.class,
                        BeanUtils.toBean(list, RsTaskBusinessRespVO.class));
    }

}
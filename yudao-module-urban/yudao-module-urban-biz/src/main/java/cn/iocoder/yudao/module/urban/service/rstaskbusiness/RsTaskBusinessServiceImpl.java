package cn.iocoder.yudao.module.urban.service.rstaskbusiness;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.urban.controller.admin.rstaskbusiness.vo.*;
import cn.iocoder.yudao.module.urban.dal.dataobject.rstaskbusiness.RsTaskBusinessDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.urban.dal.mysql.rstaskbusiness.RsTaskBusinessMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.urban.enums.ErrorCodeConstants.*;

/**
 * 任务关联业务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RsTaskBusinessServiceImpl implements RsTaskBusinessService {

    @Resource
    private RsTaskBusinessMapper rsTaskBusinessMapper;

    @Override
    public Long createRsTaskBusiness(RsTaskBusinessSaveReqVO createReqVO) {
        // 插入
        RsTaskBusinessDO rsTaskBusiness = BeanUtils.toBean(createReqVO, RsTaskBusinessDO.class);
        rsTaskBusinessMapper.insert(rsTaskBusiness);
        // 返回
        return rsTaskBusiness.getId();
    }

    @Override
    public void updateRsTaskBusiness(RsTaskBusinessSaveReqVO updateReqVO) {
        // 校验存在
        validateRsTaskBusinessExists(updateReqVO.getId());
        // 更新
        RsTaskBusinessDO updateObj = BeanUtils.toBean(updateReqVO, RsTaskBusinessDO.class);
        rsTaskBusinessMapper.updateById(updateObj);
    }

    @Override
    public void deleteRsTaskBusiness(Long id) {
        // 校验存在
        validateRsTaskBusinessExists(id);
        // 删除
        rsTaskBusinessMapper.deleteById(id);
    }

    private void validateRsTaskBusinessExists(Long id) {
        if (rsTaskBusinessMapper.selectById(id) == null) {
            throw exception(RS_TASK_BUSINESS_NOT_EXISTS);
        }
    }

    @Override
    public RsTaskBusinessDO getRsTaskBusiness(Long id) {
        return rsTaskBusinessMapper.selectById(id);
    }

    @Override
    public PageResult<RsTaskBusinessDO> getRsTaskBusinessPage(RsTaskBusinessPageReqVO pageReqVO) {
        return rsTaskBusinessMapper.selectPage(pageReqVO);
    }

}
package cn.iocoder.yudao.module.urban.controller.admin.rstaskbusiness.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 任务关联业务分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RsTaskBusinessPageReqVO extends PageParam {

    @Schema(description = "城市标准体系ID", example = "8454")
    private Long citystandardId;

    @Schema(description = "任务ID", example = "24153")
    private Long taskId;

    @Schema(description = "任务类型：住宅|小区|社区|街道", example = "2")
    private String taskType;

    @Schema(description = "业务ID ，采集对象如住宅、小区、社区", example = "29724")
    private String businessId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}